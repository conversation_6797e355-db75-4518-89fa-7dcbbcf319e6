import Dexie from 'dexie';

// 定义数据库类
class AppDB extends <PERSON><PERSON> {
  constructor() {
    super('UserManagementDB');

    // 定义数据库版本和表结构
    this.version(1).stores({
      users: '++id, name, email, department, position, status, joinDate, createdAt' // 主键是自增id
    });

    // 添加菜谱表
    this.version(2).stores({
      users: '++id, name, email, department, position, status, joinDate, createdAt',
      recipes: '++id, name, description, category, image, images, cookTime, difficulty, servings, rating, isFavorite, spicy, tags, story, ingredients, steps, createdAt, updatedAt'
    });

    this.users = this.table('users');
    this.recipes = this.table('recipes');
  }
}

// 创建数据库实例
const db = new AppDB();

// 导出数据库操作方法
export const userDB = {
  // 添加用户
  async addUser(user) {
    return await db.users.add({
      ...user,
      createdAt: new Date()
    });
  },
  
  // 更新用户
  async updateUser(id, user) {
    return await db.users.update(id, user);
  },
  
  // 删除用户
  async deleteUser(id) {
    return await db.users.delete(id);
  },
  
  // 获取所有用户
  async getAllUsers() {
    return await db.users.toArray();
  },
  
  // 根据ID获取用户
  async getUserById(id) {
    return await db.users.get(id);
  },
  
  // 搜索用户
  async searchUsers(keyword) {
    return await db.users
      .filter(user =>
        user.name.includes(keyword) ||
        user.email.includes(keyword) ||
        user.department.includes(keyword) ||
        user.position.includes(keyword)
      )
      .toArray();
  },

  // 分页获取用户
  async getUsersPaginated(page = 1, pageSize = 10) {
    const offset = (page - 1) * pageSize;
    return await db.users
      .orderBy('id')
      .offset(offset)
      .limit(pageSize)
      .toArray();
  },

  // 获取用户总数
  async getUserCount() {
    return await db.users.count();
  },

  // 清空所有用户数据
  async clearAllUsers() {
    return await db.users.clear();
  },

  // 批量添加用户
  async bulkAddUsers(users) {
    return await db.users.bulkAdd(users.map(user => ({
      ...user,
      createdAt: new Date()
    })));
  }
};

// 菜谱数据库操作方法
export const recipeDB = {
  // 添加菜谱
  async addRecipe(recipe) {
    return await db.recipes.add({
      ...recipe,
      createdAt: new Date(),
      updatedAt: new Date()
    });
  },

  // 更新菜谱
  async updateRecipe(id, recipe) {
    return await db.recipes.update(id, {
      ...recipe,
      updatedAt: new Date()
    });
  },

  // 删除菜谱
  async deleteRecipe(id) {
    return await db.recipes.delete(id);
  },

  // 获取所有菜谱
  async getAllRecipes() {
    return await db.recipes.toArray();
  },

  // 根据ID获取菜谱
  async getRecipeById(id) {
    return await db.recipes.get(id);
  },

  // 搜索菜谱
  async searchRecipes(keyword) {
    return await db.recipes
      .filter(recipe =>
        recipe.name.includes(keyword) ||
        recipe.description.includes(keyword) ||
        recipe.category.includes(keyword) ||
        recipe.tags.some(tag => tag.includes(keyword)) ||
        recipe.ingredients.some(ingredient => ingredient.name.includes(keyword))
      )
      .toArray();
  },

  // 根据分类获取菜谱
  async getRecipesByCategory(category) {
    if (category === 'all') {
      return await this.getAllRecipes();
    }
    return await db.recipes
      .filter(recipe => recipe.category === category)
      .toArray();
  },

  // 获取收藏的菜谱
  async getFavoriteRecipes() {
    return await db.recipes
      .filter(recipe => recipe.isFavorite === true)
      .toArray();
  },

  // 分页获取菜谱
  async getRecipesPaginated(page = 1, pageSize = 10) {
    const offset = (page - 1) * pageSize;
    return await db.recipes
      .orderBy('id')
      .offset(offset)
      .limit(pageSize)
      .toArray();
  },

  // 获取菜谱总数
  async getRecipeCount() {
    return await db.recipes.count();
  },

  // 清空所有菜谱数据
  async clearAllRecipes() {
    return await db.recipes.clear();
  },

  // 批量添加菜谱
  async bulkAddRecipes(recipes) {
    return await db.recipes.bulkAdd(recipes.map(recipe => ({
      ...recipe,
      createdAt: new Date(),
      updatedAt: new Date()
    })));
  },

  // 初始化示例数据
  async initializeSampleData() {
    const count = await this.getRecipeCount();
    if (count === 0) {
      const sampleRecipes = [
        {
          name: '蜜汁叉烧',
          description: '甜蜜诱人的港式经典，她的最爱',
          category: 'specialty',
          image: '',
          images: [''],
          cookTime: 45,
          difficulty: 'medium',
          servings: 2,
          rating: 5,
          isFavorite: true,
          spicy: '',
          tags: ['甜味', '港式', '下饭'],
          story: '第一次做给她吃的菜，看到她满足的笑容，我就知道这道菜要经常做了。甜蜜的味道就像我们的爱情。',
          ingredients: [
            { name: '猪梅花肉', amount: '500g' },
            { name: '蜂蜜', amount: '3勺' },
            { name: '生抽', amount: '2勺' },
            { name: '老抽', amount: '1勺' },
            { name: '料酒', amount: '1勺' },
            { name: '白糖', amount: '1勺' }
          ],
          steps: [
            {
              description: '将猪肉切成长条状，用料酒腌制30分钟',
              tip: '肉要选择肥瘦相间的部位，口感更好',
              image: ''
            },
            {
              description: '调制叉烧酱：蜂蜜、生抽、老抽、白糖混合',
              tip: '蜂蜜的量可以根据她的喜好调整',
              image: ''
            },
            {
              description: '烤箱200度预热，肉条刷酱烤25分钟',
              tip: '中途要翻面并再次刷酱，颜色会更诱人'
            }
          ]
        },
        {
          name: '番茄鸡蛋面',
          description: '简单温暖的家常味道',
          category: 'home',
          image: '',
          images: [''],
          cookTime: 15,
          difficulty: 'easy',
          servings: 1,
          rating: 4,
          isFavorite: false,
          spicy: '',
          tags: ['家常', '快手', '营养'],
          story: '忙碌的工作日，为她准备的快手营养餐。简单却充满爱意。',
          ingredients: [
            { name: '挂面', amount: '100g' },
            { name: '鸡蛋', amount: '2个' },
            { name: '番茄', amount: '2个' },
            { name: '葱花', amount: '适量' },
            { name: '盐', amount: '适量' }
          ],
          steps: [
            { description: '鸡蛋打散炒熟盛起', tip: '鸡蛋要炒得嫩一些' },
            { description: '番茄切块炒出汁水', tip: '多炒一会儿，汤汁更浓郁' },
            { description: '加水煮开，下面条煮熟即可' }
          ]
        },
        {
          name: '提拉米苏',
          description: '浪漫的意式甜品，甜蜜如初恋',
          category: 'dessert',
          image: '',
          images: [''],
          cookTime: 120,
          difficulty: 'hard',
          servings: 4,
          rating: 5,
          isFavorite: true,
          spicy: '',
          tags: ['甜品', '浪漫', '意式'],
          story: '纪念日特制的甜品，每一层都是对她的爱意。看她吃得满足的样子，所有的努力都值得。',
          ingredients: [
            { name: '马斯卡彭奶酪', amount: '250g' },
            { name: '鸡蛋', amount: '3个' },
            { name: '细砂糖', amount: '80g' },
            { name: '手指饼干', amount: '200g' },
            { name: '浓缩咖啡', amount: '200ml' },
            { name: '可可粉', amount: '适量' }
          ],
          steps: [
            { description: '制作马斯卡彭奶酪糊', tip: '温度要控制好，避免结块' },
            { description: '手指饼干蘸咖啡液铺底', tip: '不要蘸太久，饼干会散' },
            { description: '层层叠叠，冷藏4小时以上' }
          ]
        }
      ];

      await this.bulkAddRecipes(sampleRecipes);
    }
  }
};

export default db;
