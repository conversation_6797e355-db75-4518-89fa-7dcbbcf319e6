import Dexie from 'dexie';

// 定义数据库类
class AppDB extends <PERSON><PERSON> {
  constructor() {
    super('UserManagementDB');
    
    // 定义数据库版本和表结构
    this.version(1).stores({
      users: '++id, name, email, department, position, status, joinDate, createdAt' // 主键是自增id
    });
    
    this.users = this.table('users');
  }
}

// 创建数据库实例
const db = new AppDB();

// 导出数据库操作方法
export const userDB = {
  // 添加用户
  async addUser(user) {
    return await db.users.add({
      ...user,
      createdAt: new Date()
    });
  },
  
  // 更新用户
  async updateUser(id, user) {
    return await db.users.update(id, user);
  },
  
  // 删除用户
  async deleteUser(id) {
    return await db.users.delete(id);
  },
  
  // 获取所有用户
  async getAllUsers() {
    return await db.users.toArray();
  },
  
  // 根据ID获取用户
  async getUserById(id) {
    return await db.users.get(id);
  },
  
  // 搜索用户
  async searchUsers(keyword) {
    return await db.users
      .filter(user =>
        user.name.includes(keyword) ||
        user.email.includes(keyword) ||
        user.department.includes(keyword) ||
        user.position.includes(keyword)
      )
      .toArray();
  },

  // 分页获取用户
  async getUsersPaginated(page = 1, pageSize = 10) {
    const offset = (page - 1) * pageSize;
    return await db.users
      .orderBy('id')
      .offset(offset)
      .limit(pageSize)
      .toArray();
  },

  // 获取用户总数
  async getUserCount() {
    return await db.users.count();
  },

  // 清空所有用户数据
  async clearAllUsers() {
    return await db.users.clear();
  },

  // 批量添加用户
  async bulkAddUsers(users) {
    return await db.users.bulkAdd(users.map(user => ({
      ...user,
      createdAt: new Date()
    })));
  }
};

export default db;
